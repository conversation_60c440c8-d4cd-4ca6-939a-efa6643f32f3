using GamesEngine;
using GamesEngine.Business.Liquidity;
using GamesEngine.Business.Liquidity.Persistence;
using GamesEngine.PurchaseOrders;
using GamesEngine.Settings;
using Puppeteer.EventSourcing;
using System.Diagnostics;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using static GamesEngine.PurchaseOrders.CustomerMessage;

namespace LiquidityBIAPI
{
    public class Consumers
    {
        public abstract class ContainerEventsConsumer : Consumer
        {
            protected readonly IOlapRepository _repository;
            protected readonly IOlapSchemaManager _schemaManager;

            public ContainerEventsConsumer(string group, string topic, IOlapRepository repository, IOlapSchemaManager schemaManager) : base(group, topic)
            {
                _repository = repository;
                _schemaManager = schemaManager;
            }

            public static LiquidityMessageType GetType(string message)
            {
                if (string.IsNullOrEmpty(message)) throw new ArgumentException("Cannot determine message type from an empty or null message.", nameof(message));
                return (LiquidityMessageType)message[0];
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                var messageType = GetType(msg);
                switch (messageType)
                {
                    case LiquidityMessageType.LiquidCreated:
                        var createdLiquidMessage = new CreatedLiquidMessage(msg);
                        _schemaManager.CreateTablesIfNotExists(createdLiquidMessage.Kind);
                        _repository.CreateJar(createdLiquidMessage.Kind, version: 1, description: $"First Jar {createdLiquidMessage.Kind}", createdLiquidMessage.CreatedAt, previousJarId: null);

                        break;
                    case LiquidityMessageType.DraftDeposit:
                        DraftDepositMessage confirmDepositMessage = new DraftDepositMessage(msg);
                        Deposit depositToInsert = new Deposit(confirmDepositMessage);

                        _repository.CreateDeposit(confirmDepositMessage.Kind, depositToInsert);
                        _repository.CreateJarDetailIfNotExists(confirmDepositMessage.Kind, confirmDepositMessage.JarVersion, depositToInsert.Id, depositToInsert.Created);
                        break;
                    case LiquidityMessageType.ConfirmedDeposit:
                        break;
                    case LiquidityMessageType.JarCreated:
                        CreatedJarMessage createdJarMessage = new CreatedJarMessage(msg);

                        _repository.CreateJar(createdJarMessage.Kind, createdJarMessage.Version, createdJarMessage.Description, createdJarMessage.CreatedAt, createdJarMessage.PreviousVersion);

                        break;
                    case LiquidityMessageType.BottleCreated:
                        CreatedBottleMessage createdBottleMessage = new CreatedBottleMessage(msg);
                        Bottle bottle = new Bottle(createdBottleMessage);
                        _repository.CreateBottle(createdBottleMessage.Kind, bottle);
                        break;
                    case LiquidityMessageType.DispenserCreated:
                        CreatedDispenserMessage createdDispenserMessage = new CreatedDispenserMessage(msg);
                        Dispenser dispenser = new Dispenser(createdDispenserMessage);
                        _repository.CreateDispenser(createdDispenserMessage.Kind, dispenser);
                        break;
                    case LiquidityMessageType.WithdrawalCreated:
                        CreatedWithdrawalMessage createdWithdrawalMessage = new CreatedWithdrawalMessage(msg);
                        var withdrawalToInsert = new Withdrawal(createdWithdrawalMessage);

                        _repository.CreateWithdrawal(createdWithdrawalMessage.Kind, withdrawalToInsert);
                        _repository.CreateDispenserDetailIfNotExists(createdWithdrawalMessage.Kind, createdWithdrawalMessage.Id, createdWithdrawalMessage.DispenserId, createdWithdrawalMessage.CreatedDate);
                        break;
                    case LiquidityMessageType.InvoicePaid:
                        break;
                    case LiquidityMessageType.TankDiscarded:
                        var discardedTankMessage = new TankDiscardedMessage(msg);
                        var createdTank = new Tank(discardedTankMessage);

                        _repository.CreateTank(discardedTankMessage.Kind, createdTank);
                        if (createdTank.DepositIds != null && createdTank.DepositIds.Any())
                        {
                            _repository.CreateTankDetails(discardedTankMessage.Kind, createdTank.Id, createdTank.Version, createdTank.DepositIds, createdTank.Created);
                        }
                        break;
                    case LiquidityMessageType.TankArchived:
                        var archivedTankMessage = new TankArchivedMessage(msg);
                        var archivedTank = new Tank(archivedTankMessage);

                        _repository.CreateTank(archivedTankMessage.Kind, archivedTank);
                        if (archivedTank.DepositIds != null && archivedTank.DepositIds.Any())
                        {
                            _repository.CreateTankDetails(archivedTankMessage.Kind, archivedTank.Id, archivedTank.Version, archivedTank.DepositIds, archivedTank.Created);
                        }
                        break;
                    case LiquidityMessageType.TankerArchived:
                        var archivedTankerMessage = new TankerArchivedMessage(msg);
                        var archivedTanker = new Tanker(archivedTankerMessage);

                        _repository.CreateTanker(archivedTankerMessage.Kind, archivedTanker);
                        break;
                    default:
                        var e = new GameEngineException($"There is no implementation for {nameof(messageType)} {messageType}");
                        Loggers.GetIntance().Emails.Error($"Unknown LiquidityMessageType encountered: {messageType}", e);
                        throw e;
                }
                
            }
        }

        public class OlapContainerEventsConsumer : ContainerEventsConsumer
        {
            public OlapContainerEventsConsumer(string group, string topic, IOlapRepository repository, IOlapSchemaManager schemaManager)
                : base(group, topic, repository, schemaManager) { }
        }

        public class SearchContainerEventsConsumer : Consumer
        {
            private string topic;
            private ISearchStorage _storage;

            public SearchContainerEventsConsumer(string group, string topic, ISearchStorage storage) : base(group, topic)
            {
                this.topic = topic;
                _storage = storage;
            }

            public override void OnMessageBeforeCommit(string msg)
            {
                var messageType = ContainerEventsConsumer.GetType(msg);
                switch (messageType)
                {
                    case LiquidityMessageType.LiquidCreated:
                        var createdLiquidMessage = new CreatedLiquidMessage(msg);
                        _storage.CreateTablesIfNotExists(createdLiquidMessage.Kind);
                        _storage.CreateJar(createdLiquidMessage.Kind, version: 1, description: $"First Jar {createdLiquidMessage.Kind}", createdLiquidMessage.CreatedAt, previousJarId: null);

                        break;
                    case LiquidityMessageType.DraftDeposit:
                        var confirmDepositMessage = new DraftDepositMessage(msg);
                        Deposit depositToInsert = new Deposit(confirmDepositMessage);

                        _storage.CreateDeposit(confirmDepositMessage.Kind, depositToInsert);

                        break;
                    case LiquidityMessageType.ConfirmedDeposit:
                        break;
                    case LiquidityMessageType.JarCreated:
                        CreatedJarMessage createdJarMessage = new CreatedJarMessage(msg);

                        _storage.CreateJar(createdJarMessage.Kind, createdJarMessage.Version, createdJarMessage.Description, createdJarMessage.CreatedAt, createdJarMessage.PreviousVersion);

                        break;
                    case LiquidityMessageType.BottleCreated:
                        CreatedBottleMessage createdBottleMessage = new CreatedBottleMessage(msg);
                        Bottle bottle = new Bottle(createdBottleMessage);
                        _storage.CreateBottle(createdBottleMessage.Kind, bottle);
                        break;
                    case LiquidityMessageType.DispenserCreated:
                        CreatedDispenserMessage createdDispenserMessage = new CreatedDispenserMessage(msg);
                        Dispenser dispenser = new Dispenser(createdDispenserMessage);
                        _storage.CreateDispenser(createdDispenserMessage.Kind, dispenser);
                        break;
                    case LiquidityMessageType.WithdrawalCreated:
                        var createdWithdrawalMessage = new CreatedWithdrawalMessage(msg);
                        var withdrawalToInsert = new Withdrawal(createdWithdrawalMessage);
                        _storage.CreateWithdrawal(createdWithdrawalMessage.Kind, withdrawalToInsert);
                        break;
                    case LiquidityMessageType.InvoicePaid:
                        var invoicePaidMessage = new InvoicePaidMessage(msg);
                        var invoicePaymentToInsert = new InvoicePayment(invoicePaidMessage);
                        _storage.CreateInvoicePayment(invoicePaidMessage.Kind, invoicePaymentToInsert);
                        break;
                    case LiquidityMessageType.TankDiscarded:
                        var discardedTankMessage = new TankDiscardedMessage(msg);
                        var createdTank = new Tank(discardedTankMessage);

                        _storage.CreateTank(discardedTankMessage.Kind, createdTank);
                        break;
                    case LiquidityMessageType.TankArchived:
                        var archivedTankMessage = new TankArchivedMessage(msg);
                        var archivedTank = new Tank(archivedTankMessage);

                        _storage.CreateTank(archivedTankMessage.Kind, archivedTank);
                        break;
                    case LiquidityMessageType.TankerArchived:
                        var archivedTankerMessage = new TankerArchivedMessage(msg);
                        var archivedTanker = new Tanker(archivedTankerMessage);

                        _storage.CreateTanker(archivedTankerMessage.Kind, archivedTanker);
                        break;
                    default:
                        var e = new GameEngineException($"There is no implementation for {nameof(messageType)} {messageType}");
                        Loggers.GetIntance().Emails.Error($"Unknown LiquidityMessageType encountered: {messageType}", e);
                        throw e;
                }

            }
        }


        internal static void CreateConsumerForOlap(IOlapRepository olapRepository, IOlapSchemaManager olapSchemaManager)
        {
            new OlapContainerEventsConsumer(
                Integration.Kafka.Group + "_olap",
                Integration.Kafka.TopicForContainerEvents,
                olapRepository,
                olapSchemaManager
            ).StartListening();
            Debug.WriteLine($"Started Olap Consumer. Group: {Integration.Kafka.Group + "_olap"}, Topic: {Integration.Kafka.TopicForContainerEvents}");
        }

        internal static void CreateConsumerForSearchEngine(ISearchStorage searchStorage)
        {
            new SearchContainerEventsConsumer(Integration.Kafka.Group + "_search", Integration.Kafka.TopicForContainerEvents, searchStorage).StartListening();
            Debug.WriteLine($"Started Search Consumer. Group: {Integration.Kafka.Group + "_search"}, Topic: {Integration.Kafka.TopicForContainerEvents}");
        }
    }
}
