﻿using GamesEngine.Business.Liquidity.Persistence;
using static GamesEngine.Business.Liquidity.Persistence.DataModel;
using System.Data;
using ClickHouse.Client.ADO.Parameters;

namespace GamesEngine.Business.Liquidity.IntegrationTests
{
    [TestClass]
    public class OlapIntegrationTests
    {
        private static IDbConnectionFactory _connectionFactory;
        private static IOlapSchemaManager _schemaManager;
        private static IOlapRepository _repository;

        // A unique "kind" for this test run to avoid conflicts if other tests run in parallel
        // against the same database instance.
        private const string TEST_KIND = "integrationtest";

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            // The connection string for the Docker container.
            // Using an environment variable is a good practice for CI/CD pipelines.
            // You can set this in your system, your IDE's launch settings, or a .runsettings file.
            var connectionString = Environment.GetEnvironmentVariable("CLICKHOUSE_CONNECTION_STRING");
            if (string.IsNullOrEmpty(connectionString))
            {
                connectionString = "Host=localhost;Port=8124;Database=default;User=default;Password=********";
            }

            _connectionFactory = new OlapConnectionFactory(connectionString);
            _schemaManager = new OlapSchemaManager(_connectionFactory);
            _repository = new OlapRepository(_connectionFactory);

            // Initial cleanup in case of a previous failed run
            _schemaManager.DropAllTables(TEST_KIND);
        }

        [TestInitialize]
        public void TestInitialize()
        {
            // This runs before EACH test. It ensures every test starts with a clean slate.
            _schemaManager.DropAllTables(TEST_KIND);
            _schemaManager.CreateTablesIfNotExists(TEST_KIND);
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            // Clean up the database after all tests in the class have run.
            _schemaManager?.DropAllTables(TEST_KIND);
        }

        [TestMethod]
        public void OlapSchemaManager_CreateAndDropTables_Succeeds()
        {
            // Arrange: The TestInitialize has already created the tables.

            // Act
            _schemaManager.DropAllTables(TEST_KIND);
            _schemaManager.CreateTablesIfNotExists(TEST_KIND);

            // Assert: The primary assertion is that the methods executed without throwing an exception.
            // A more robust test could query the `system.tables` to verify their existence.
            Assert.IsTrue(true, "Schema creation and deletion completed without errors.");
        }

        [TestMethod]
        public void OlapRepository_CreateDeposit_SucceedsAndDataIsPersisted()
        {
            // Arrange
            var deposit = new Deposit
            {
                Id = 101,
                DocumentNumber = "DOC-101",
                Amount = 150.75m,
                Date = DateTime.UtcNow,
                StoreId = 1,
                AccountNumber = "ACC-001",
                DomainId = 1,
                Address = "123 Test St",
                Created = DateTime.UtcNow
            };

            // Act
            _repository.CreateDeposit(TEST_KIND, deposit);

            // Assert
            // Verify by reading the data back directly from the database.
            using (var connection = _connectionFactory.CreateConnection())
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = $"SELECT count() FROM {OlapTableDefinitions.DynamicTableName("deposit", TEST_KIND)} WHERE Id = 101";
                    var count = (ulong)command.ExecuteScalar();
                    Assert.AreEqual(1UL, count, "The deposit record was not found in the database.");
                }
            }
        }

        [TestMethod]
        public void OlapRepository_CreateTankAndBulkInsertDetails_Succeeds()
        {
            // Arrange
            var tank = new Tank
            {
                Id = 202,
                Description = "Main Test Tank",
                Created = DateTime.UtcNow,
                OriginType = "Jar",
                OriginId = 99
            };

            var depositsToCreate = new List<Deposit>
            {
                new Deposit { Id = 301, DocumentNumber = "D301", Amount = 10, Date = DateTime.UtcNow, AccountNumber = "A1", Created = DateTime.UtcNow },
                new Deposit { Id = 302, DocumentNumber = "D302", Amount = 20, Date = DateTime.UtcNow, AccountNumber = "A1", Created = DateTime.UtcNow },
                new Deposit { Id = 303, DocumentNumber = "D303", Amount = 30, Date = DateTime.UtcNow, AccountNumber = "A1", Created = DateTime.UtcNow }
            };

            // We must create the deposits first, as tankdetail has a dependency.
            foreach (var dep in depositsToCreate)
            {
                _repository.CreateDeposit(TEST_KIND, dep);
            }

            var depositIds = depositsToCreate.Select(d => (int)d.Id);

            // Act
            _repository.CreateTank(TEST_KIND, tank);
            _repository.CreateTankDetails(TEST_KIND, tank.Id, depositIds, DateTime.UtcNow);

            // Assert
            using (var connection = _connectionFactory.CreateConnection())
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    // Verify the tank exists
                    command.CommandText = $"SELECT count() FROM {OlapTableDefinitions.DynamicTableName("tank", TEST_KIND)} WHERE Id = @id";
                    command.Parameters.Add(new ClickHouseDbParameter { ParameterName = "id", Value = tank.Id, DbType = DbType.Int64 });
                    var tankCount = (ulong)command.ExecuteScalar();
                    Assert.AreEqual(1UL, tankCount, "The tank record was not created.");

                    // Verify the bulk-inserted details exist
                    command.CommandText = $"SELECT count() FROM {OlapTableDefinitions.DynamicTableName("tankdetail", TEST_KIND)} WHERE TankId = @id";
                    var detailCount = (ulong)command.ExecuteScalar();
                    Assert.AreEqual((ulong)depositsToCreate.Count, detailCount, "The number of tank detail records is incorrect.");
                }
            }
        }

        [TestMethod]
        public void OlapRepository_CreateWithdrawalAndDispenser_Succeeds()
        {
            // Arrange
            var withdrawal = new Withdrawal
            {
                Id = 401,
                DocumentNumber = "W-401",
                Amount = 50.0m,
                Date = DateTime.UtcNow.AddHours(-1),
                StoreId = 2,
                AccountNumber = "ACC-002",
                DomainId = 1,
                Address = "456 Test Ave",
                Created = DateTime.UtcNow
            };

            var dispenser = new Dispenser
            {
                Id = 501,
                Description = "Cash Dispenser A",
                Created = DateTime.UtcNow
            };

            // Act
            _repository.CreateWithdrawal(TEST_KIND, withdrawal);
            _repository.CreateDispenser(TEST_KIND, dispenser);
            _repository.CreateDispenserDetailIfNotExists(TEST_KIND, withdrawal.Id, dispenser.Id, DateTime.UtcNow);

            // Assert
            using (var connection = _connectionFactory.CreateConnection())
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = $"SELECT count() FROM {OlapTableDefinitions.DynamicTableName("withdrawal", TEST_KIND)} WHERE Id = @id";
                    command.Parameters.Add(new ClickHouseDbParameter { ParameterName = "id", Value = withdrawal.Id });
                    Assert.AreEqual(1UL, (ulong)command.ExecuteScalar(), "Withdrawal was not created.");

                    command.CommandText = $"SELECT count() FROM {OlapTableDefinitions.DynamicTableName("dispenser", TEST_KIND)} WHERE Id = @id";
                    command.Parameters[0].Value = dispenser.Id;
                    Assert.AreEqual(1UL, (ulong)command.ExecuteScalar(), "Dispenser was not created.");

                    command.CommandText = $"SELECT count() FROM {OlapTableDefinitions.DynamicTableName("dispenserdetail", TEST_KIND)} WHERE WithdrawalId = @wid AND DispenserId = @did";
                    command.Parameters.Clear();
                    command.Parameters.Add(new ClickHouseDbParameter { ParameterName = "wid", Value = withdrawal.Id });
                    command.Parameters.Add(new ClickHouseDbParameter { ParameterName = "did", Value = dispenser.Id });
                    Assert.AreEqual(1UL, (ulong)command.ExecuteScalar(), "Dispenser detail was not created.");
                }
            }
        }

        [TestMethod]
        public void OlapRepository_CreateTankerWithName_SucceedsAndNameIsRetrieved()
        {
            // Arrange
            var tanker = new Tanker
            {
                Id = 601,
                Name = "Test Tanker Name",
                Description = "Test Tanker Description",
                Created = DateTime.UtcNow
            };

            var deposit = new Deposit
            {
                Id = 701,
                DocumentNumber = "DOC-701",
                Amount = 100.0m,
                Date = DateTime.UtcNow,
                StoreId = 1,
                AccountNumber = "ACC-701",
                DomainId = 1,
                Address = "789 Test Blvd",
                Created = DateTime.UtcNow
            };

            // Act
            _repository.CreateDeposit(TEST_KIND, deposit);
            _repository.CreateTanker(TEST_KIND, tanker);
            _repository.CreateTankerDetailIfNotExists(TEST_KIND, tanker.Id, deposit.Id, DateTime.UtcNow);

            // Retrieve the tanker with deposits using the query service
            var olapQueryService = new OlapQueryService(_connectionFactory);
            var result = olapQueryService.TankerAndAllItsDepositsAsync(TEST_KIND, tanker.Id).Result;

            // Assert
            Assert.IsNotNull(result, "TankerWithDeposits result should not be null.");
            Assert.IsNotNull(result.TankerInfo, "TankerInfo should not be null.");
            Assert.AreEqual(tanker.Id, result.TankerInfo.Id, "Tanker ID should match.");
            Assert.AreEqual(tanker.Name, result.TankerInfo.Name, "Tanker Name should match.");
            Assert.AreEqual(tanker.Description, result.TankerInfo.Description, "Tanker Description should match.");
            Assert.AreEqual(1, result.Deposits.Count, "Should have one deposit.");
            Assert.AreEqual(deposit.Id, result.Deposits.First().Id, "Deposit ID should match.");
        }

        [TestMethod]
        public void OlapRepository_CreateTankWithVersion_SucceedsAndVersionIsPersisted()
        {
            // Arrange
            var tank = new Tank
            {
                Id = 801,
                Name = "Test Tank With Version",
                Description = "Tank with version test",
                Created = DateTime.UtcNow,
                OriginType = "Jar",
                OriginId = 5,
                Version = 3
            };

            // Act
            _repository.CreateTank(TEST_KIND, tank);

            // Assert
            using (var connection = _connectionFactory.CreateConnection())
            {
                connection.Open();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = $"SELECT Version FROM {OlapTableDefinitions.DynamicTableName("tank", TEST_KIND)} WHERE Id = @id";
                    command.Parameters.Add(new ClickHouseDbParameter { ParameterName = "id", Value = tank.Id, DbType = DbType.Int64 });
                    var version = (int)command.ExecuteScalar();
                    Assert.AreEqual(tank.Version, version, "The tank version was not persisted correctly.");
                }
            }
        }

        [TestMethod]
        public void DataModel_TankConstructor_IncludesVersionProperty()
        {
            // Arrange - Create a mock TankDiscardedMessage by setting properties directly
            var tankDiscardedMessage = new GamesEngine.Business.Liquidity.TankDiscardedMessage("");
            // Use reflection to set internal properties for testing
            var tankIdProperty = typeof(GamesEngine.Business.Liquidity.TankDiscardedMessage).GetProperty("TankId");
            var nameProperty = typeof(GamesEngine.Business.Liquidity.TankDiscardedMessage).GetProperty("Name");
            var descriptionProperty = typeof(GamesEngine.Business.Liquidity.TankDiscardedMessage).GetProperty("Description");
            var versionProperty = typeof(GamesEngine.Business.Liquidity.TankDiscardedMessage).GetProperty("Version");
            var createdAtProperty = typeof(GamesEngine.Business.Liquidity.TankDiscardedMessage).GetProperty("CreatedAt");
            var depositIdsProperty = typeof(GamesEngine.Business.Liquidity.TankDiscardedMessage).GetProperty("DepositIds");

            tankIdProperty?.SetValue(tankDiscardedMessage, 901);
            nameProperty?.SetValue(tankDiscardedMessage, "Test Tank");
            descriptionProperty?.SetValue(tankDiscardedMessage, "Test Description");
            versionProperty?.SetValue(tankDiscardedMessage, 5);
            createdAtProperty?.SetValue(tankDiscardedMessage, DateTime.UtcNow);
            depositIdsProperty?.SetValue(tankDiscardedMessage, new List<int> { 1, 2, 3 });

            // Act
            var tank = new Tank(tankDiscardedMessage);

            // Assert
            Assert.AreEqual(901, tank.Id, "Tank ID should match message TankId");
            Assert.AreEqual("Test Tank", tank.Name, "Tank Name should match message Name");
            Assert.AreEqual("Test Description", tank.Description, "Tank Description should match message Description");
            Assert.AreEqual(5, tank.Version, "Tank Version should match message Version");
            Assert.AreEqual("Jar", tank.OriginType, "Tank OriginType should be 'Jar'");
            Assert.AreEqual(5, tank.OriginId, "Tank OriginId should match message Version");
        }
    }
}